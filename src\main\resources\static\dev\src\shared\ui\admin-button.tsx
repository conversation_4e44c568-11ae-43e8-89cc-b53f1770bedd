import { FC, ReactNode, useState } from "react";
import { ParsedButtonAttributes } from "../model/admin-button-types";
import { handleAdminButton } from "../lib/admin-button-handlers";
import { createButtonTooltip } from "../utils/admin-button-parser";
import { UiAdminButton } from "./admin";

interface AdminButtonProps {
  attributes: ParsedButtonAttributes;
  children: ReactNode;
  className?: string;
}

export const AdminButton: FC<AdminButtonProps> = ({
  attributes,
  children,
  className = "",
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const tooltip = createButtonTooltip(attributes);

  const handleClick = async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      await handleAdminButton(attributes);
    } finally {
      setIsLoading(false);
    }
  };

  const baseClasses = `
    px-4 py-2 
    bg-blue-600 hover:bg-blue-700 
    text-white font-medium 
    rounded-md 
    transition-colors duration-200
    disabled:opacity-50 disabled:cursor-not-allowed
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
  `
    .trim()
    .replace(/\s+/g, " ");

  return (
    <UiAdminButton
      onClick={handleClick}
      disabled={isLoading}
      title={tooltip}
      className={`${baseClasses} ${className}`}
    >
      {isLoading ? (
        <span className="flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          Загрузка...
        </span>
      ) : (
        children
      )}
    </UiAdminButton>
  );
};
