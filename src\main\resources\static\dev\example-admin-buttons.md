# Примеры использования админских кнопок

## Кнопка выбора сцены (синяя)
<button type="choose-scene" scene-id="tavern_main">Перейти в таверну</button>

## Кнопка информации о NPC (зеленая)
<button type="npc-info" npc-id="1" description="Таверщик Боб">Показать информацию о NPC</button>

## Кнопка управления магазином (фиолетовая)
<button type="shop-open-close">Открыть/закрыть магазин</button>

## Кнопка обновления шага квеста (оранжевая)
<button type="update-quest-step" quest-id="main_quest" quest-step="3">Перейти к шагу 3</button>

---

### Описание API вызовов:

1. **Выбор сцены**: `POST /admin/adventure/v1/choose-scene` с телом `"tavern_main"`
2. **Информация о NPC**: Пока заглушка, будет реализовано позже
3. **Управление магазином**: `POST /admin/adventure/v1/shop-enable` с телом `true/false`
4. **Обновление шага квеста**: `POST /admin/adventure/v1/update-quest-step` с телом `{"questId": "main_quest", "questStep": 3}`
