import { FC, useState, useEffect, useCallback } from "react";
import { AdminMarkdownWithButtons } from "./admin-markdown-with-buttons";
// import { getAdminInfo } from '@/shared/api/admin-api'; // Будет использоваться позже

export interface AdminControlPanelProps {
  className?: string;
}

/**
 * Главный компонент панели управления администратора
 */
export const AdminControlPanel: FC<AdminControlPanelProps> = ({
  className = "",
}) => {
  const [content, setContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Моковый контент для демонстрации
  const mockContent = `
# Панель управления администратора

## Управление сценой

Текущая сцена: **Таверна "Золотой дракон"**

<button type="choose-scene" scene-id="tavern_main">Перейти в главный зал таверны</button>

<button type="choose-scene" scene-id="tavern_basement">Спуститься в подвал</button>

## Управление NPC

В текущей локации находятся следующие персонажи:

- **Бармен Торгрим** - <button type="npc-info" npc-id="1" description="Опытный дварф-бармен, знает много историй">Показать информацию</button>
- **Странник в капюшоне** - <button type="npc-info" npc-id="2" description="Загадочная фигура в углу таверны">Показать информацию</button>
- **Торговец Элрик** - <button type="npc-info" npc-id="3" description="Торговец редкими товарами">Показать информацию</button>

## Управление магазином

<button type="shop-open-close">Открыть магазин</button>

---

> **Подсказка:** Используйте кнопки выше для управления игровым процессом. Все действия будут применены немедленно.
  `;

  const loadAdminInfo = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Пока используем моковый контент
      // В будущем раскомментировать эту строку:
      // const data = await getAdminInfo();

      // Имитируем загрузку
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setContent(mockContent);
    } catch (err) {
      console.error("Failed to load admin info:", err);
      setError("Не удалось загрузить информацию панели управления");
      setContent(mockContent); // Fallback на моковый контент
    } finally {
      setIsLoading(false);
    }
  }, [mockContent]);

  useEffect(() => {
    loadAdminInfo();
  }, [loadAdminInfo]);

  const handleRefresh = () => {
    loadAdminInfo();
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Загрузка панели управления...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Заголовок панели */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <button
          onClick={handleRefresh}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
          title="Обновить"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </button>
      </div>

      {/* Контент панели */}
      <div className="p-4">
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <AdminMarkdownWithButtons content={content} />
      </div>
    </div>
  );
};
