import { adminInfoApi, adminAdventure<PERSON><PERSON> } from "./base-api";

/**
 * API для получения информации панели управления
 */
export const getAdminInfo = async (): Promise<string> => {
  const response = await adminInfoApi.get("");
  return response.data;
};

/**
 * API для перехода в сцену
 */
export const chooseScene = async (sceneId: string): Promise<void> => {
  await adminAdventureApi.post("/choose-scene", sceneId);
};

/**
 * API для управления магазином
 */
export const toggleShop = async (enabled: boolean): Promise<void> => {
  await adminAdventureApi.post("/shop-enable", enabled);
};

/**
 * API для обновления шага квеста
 */
export const updateQuestStep = async (
  questId: string,
  questStep: number,
): Promise<void> => {
  await adminAdventureApi.post("/update-quest-step", {
    questId,
    questStep,
  });
};

/**
 * Интерфейс для информации о NPC
 */
export interface NpcInfo {
  id: string;
  name: string;
  description: string;
}

/**
 * Получение информации о NPC (пока заглушка)
 */
export const getNpcInfo = async (npcId: string): Promise<NpcInfo> => {
  // TODO: Реализовать когда будет готов API
  return Promise.resolve({
    id: npcId,
    name: `NPC ${npcId}`,
    description: "Информация о NPC будет загружена позже",
  });
};
