/**
 * Типы кнопок для панели управления администратора
 */
export enum AdminButtonType {
  CHOOSE_SCENE = "choose-scene",
  NPC_INFO = "npc-info",
  SHOP_OPEN_CLOSE = "shop-open-close",
  UPDATE_QUEST_STEP = "update-quest-step",
}

/**
 * Базовые атрибуты для всех кнопок
 */
export interface BaseButtonAttributes {
  type: AdminButtonType;
}

/**
 * Атрибуты для кнопки перехода в сцену
 */
export interface ChooseSceneButtonAttributes extends BaseButtonAttributes {
  type: AdminButtonType.CHOOSE_SCENE;
  "scene-id": string;
}

/**
 * Атрибуты для кнопки информации о NPC
 */
export interface NpcInfoButtonAttributes extends BaseButtonAttributes {
  type: AdminButtonType.NPC_INFO;
  description: string;
  "npc-id": string;
}

/**
 * Атрибуты для кнопки открытия/закрытия магазина
 */
export interface ShopOpenCloseButtonAttributes extends BaseButtonAttributes {
  type: AdminButtonType.SHOP_OPEN_CLOSE;
}

/**
 * Атрибуты для кнопки обновления шага квеста
 */
export interface UpdateQuestStepButtonAttributes extends BaseButtonAttributes {
  type: AdminButtonType.UPDATE_QUEST_STEP;
  "quest-id": string;
  "quest-step": string;
}

/**
 * Объединенный тип всех возможных атрибутов кнопок
 */
export type AdminButtonAttributes =
  | ChooseSceneButtonAttributes
  | NpcInfoButtonAttributes
  | ShopOpenCloseButtonAttributes
  | UpdateQuestStepButtonAttributes;

/**
 * Данные для обработки кнопки
 */
export interface AdminButtonData {
  type: AdminButtonType;
  attributes: Record<string, string>;
  children: React.ReactNode;
}

/**
 * Результат парсинга атрибутов кнопки
 */
export interface ParsedButtonAttributes {
  type: AdminButtonType | null;
  sceneId?: string;
  npcId?: string;
  description?: string;
  questId?: string;
  questStep?: number;
}
