import viteReact from "@vitejs/plugin-react";
import path from "path";
import { defineConfig, loadEnv } from 'vite'

export default defineConfig( ({ mode }) => {
      const env = loadEnv(mode, process.cwd())

      return {
          base: env.VITE_BASE,
          plugins: [viteReact()],
          resolve: {
            alias: {
              "@": path.resolve(__dirname, "src"),
            },
          },
          build: {
            outDir: path.resolve(__dirname, "../production"),
            emptyOutDir: true,
            assetsDir: "assets",
          },
          server: {
            proxy: {
              "/player/api/v1": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },

              "/player/shop/v1": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/player/items/v1/": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/login": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/player/action/start": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/player/action/choose-targets": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/player/events/v1/notifications": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
              "/admin/info/v1": {
                target: "http://localhost:8080",
                changeOrigin: true,
                secure: false,
              },
            },
          },
      }}
);
