import {
  AdminButtonType,
  ParsedButtonAttributes,
} from "../model/admin-button-types";
import {
  chooseScene,
  toggleShop,
  getNpcInfo,
  updateQuestStep,
} from "../api/admin-api";

/**
 * Обработчик для кнопки перехода в сцену
 */
export const handleChooseScene = async (
  attributes: ParsedButtonAttributes,
): Promise<void> => {
  if (!attributes.sceneId) {
    console.error("Scene ID is required for choose-scene button");
    return;
  }

  try {
    await chooseScene(attributes.sceneId);
    console.log(`Successfully navigated to scene: ${attributes.sceneId}`);
  } catch (error) {
    console.error("Failed to navigate to scene:", error);
    // TODO: Показать уведомление об ошибке пользователю
  }
};

/**
 * Обработчик для кнопки информации о NPC
 */
export const handleNpcInfo = async (
  attributes: ParsedButtonAttributes,
): Promise<void> => {
  if (!attributes.npcId) {
    console.error("NPC ID is required for npc-info button");
    return;
  }

  try {
    const npcInfo = await getNpcInfo(attributes.npcId);
    console.log("NPC Info:", npcInfo);

    // TODO: Открыть модальное окно с информацией о NPC
    // Пока просто логируем информацию
    alert(
      `NPC Info:\nID: ${npcInfo.id}\nName: ${npcInfo.name}\nDescription: ${npcInfo.description}`,
    );
  } catch (error) {
    console.error("Failed to get NPC info:", error);
    // TODO: Показать уведомление об ошибке пользователю
  }
};

/**
 * Обработчик для кнопки открытия/закрытия магазина
 */
export const handleShopToggle = async (): Promise<void> => {
  try {
    // TODO: Получить текущее состояние магазина и переключить его
    // Пока используем true как значение по умолчанию
    await toggleShop(true);
    console.log("Shop toggle successful");
    // TODO: Обновить состояние магазина в UI
  } catch (error) {
    console.error("Failed to toggle shop:", error);
    // TODO: Показать уведомление об ошибке пользователю
  }
};

/**
 * Обработчик для кнопки обновления шага квеста
 */
export const handleUpdateQuestStep = async (
  attributes: ParsedButtonAttributes,
): Promise<void> => {
  if (!attributes.questId || typeof attributes.questStep !== "number") {
    console.error(
      "Quest ID and quest step are required for update-quest-step button",
    );
    return;
  }

  try {
    await updateQuestStep(attributes.questId, attributes.questStep);
    console.log(
      `Successfully updated quest ${attributes.questId} to step ${attributes.questStep}`,
    );
  } catch (error) {
    console.error("Failed to update quest step:", error);
    // TODO: Показать уведомление об ошибке пользователю
  }
};

/**
 * Главный обработчик кнопок
 */
export const handleAdminButton = async (
  attributes: ParsedButtonAttributes,
): Promise<void> => {
  if (!attributes.type) {
    console.error("Button type is required");
    return;
  }

  switch (attributes.type) {
    case AdminButtonType.CHOOSE_SCENE:
      await handleChooseScene(attributes);
      break;

    case AdminButtonType.NPC_INFO:
      await handleNpcInfo(attributes);
      break;

    case AdminButtonType.SHOP_OPEN_CLOSE:
      await handleShopToggle();
      break;

    case AdminButtonType.UPDATE_QUEST_STEP:
      await handleUpdateQuestStep(attributes);
      break;

    default:
      console.error(`Unknown button type: ${attributes.type}`);
  }
};
