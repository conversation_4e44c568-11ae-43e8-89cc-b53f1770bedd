import { GameModeType } from "@/entities/game/model";
import {
  GameLocation,
  GameMode,
  GameHeroes,
  GameHero,
} from "@/entities/game/ui";
import { FC } from "react";

type AdminPanelProps = {
  gameMode: GameModeType;
  location: string;
  players: GameHero[];
  npcs: GameHero[];
};

export const AdminPanelView: FC<AdminPanelProps> = ({
  gameMode,
  location,
  npcs,
  players,
}) => {
  return (
    <div className="w-[250px] h-full">
      <div className="h-full flex flex-col gap-5">
        <div>
          <GameMode gameMode={gameMode} />
          <GameLocation>{location}</GameLocation>
        </div>
        <div>
          <GameHeroes className="max-w-[155px]" npcs={npcs} players={players} />
        </div>
      </div>
    </div>
  );
};
