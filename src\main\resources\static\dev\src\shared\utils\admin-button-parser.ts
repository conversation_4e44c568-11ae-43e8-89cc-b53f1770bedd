import {
  AdminButtonType,
  ParsedButtonAttributes,
} from "../model/admin-button-types";

/**
 * Парсит атрибуты кнопки из HTML-элемента
 */
export const parseButtonAttributes = (
  properties: Record<string, any>,
): ParsedButtonAttributes => {
  const type = properties?.type?.toString() as AdminButtonType;

  if (!type || !Object.values(AdminButtonType).includes(type)) {
    return { type: null };
  }

  const result: ParsedButtonAttributes = { type };

  switch (type) {
    case AdminButtonType.CHOOSE_SCENE:
      result.sceneId =
        properties?.["sceneId"]?.toString() ||
        properties?.["scene-id"]?.toString();
      break;

    case AdminButtonType.NPC_INFO:
      result.npcId =
        properties?.["npcId"]?.toString() || properties?.["npc-id"]?.toString();
      result.description = properties?.description?.toString();
      break;

    case AdminButtonType.SHOP_OPEN_CLOSE:
      // Для этого типа кнопки дополнительные атрибуты не нужны
      break;

    case AdminButtonType.UPDATE_QUEST_STEP:
      result.questId =
        properties?.["questId"]?.toString() ||
        properties?.["quest-id"]?.toString();
      const questStepStr =
        properties?.["questStep"]?.toString() ||
        properties?.["quest-step"]?.toString();
      result.questStep = questStepStr ? parseInt(questStepStr, 10) : undefined;
      break;

    default:
      return { type: null };
  }

  return result;
};

/**
 * Валидирует атрибуты кнопки
 */
export const validateButtonAttributes = (
  attributes: ParsedButtonAttributes,
): boolean => {
  if (!attributes.type) {
    return false;
  }

  switch (attributes.type) {
    case AdminButtonType.CHOOSE_SCENE:
      return !!attributes.sceneId;

    case AdminButtonType.NPC_INFO:
      return !!attributes.npcId;

    case AdminButtonType.SHOP_OPEN_CLOSE:
      return true;

    case AdminButtonType.UPDATE_QUEST_STEP:
      return (
        !!attributes.questId &&
        typeof attributes.questStep === "number" &&
        !isNaN(attributes.questStep)
      );

    default:
      return false;
  }
};

/**
 * Создает tooltip текст для кнопки
 */
export const createButtonTooltip = (
  attributes: ParsedButtonAttributes,
): string => {
  switch (attributes.type) {
    case AdminButtonType.CHOOSE_SCENE:
      return `Перейти в сцену: ${attributes.sceneId}`;

    case AdminButtonType.NPC_INFO:
      return attributes.description || `Информация о NPC: ${attributes.npcId}`;

    case AdminButtonType.SHOP_OPEN_CLOSE:
      return "Открыть/закрыть магазин";

    case AdminButtonType.UPDATE_QUEST_STEP:
      return `Обновить квест ${attributes.questId} до шага ${attributes.questStep}`;

    default:
      return "";
  }
};
